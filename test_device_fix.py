#!/usr/bin/env python3
"""
Test script to verify the device mismatch fix for multi-GPU SAE training.
"""

import torch
import logging
from itas.core.config import ModelConfig, DatasetConfig, TrainingConfig, SAEConfig
from itas.core.model_loader import UniversalModelLoader
from itas.core.dataset_manager import DatasetManager
from itas.core.activations_store import ActivationsStore

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_device_detection():
    """Test the device detection functionality."""
    print("🧪 Testing device detection fix...")
    
    # Check available GPUs
    num_gpus = torch.cuda.device_count()
    print(f"📊 Available GPUs: {num_gpus}")
    
    if num_gpus < 2:
        print("⚠️  Need at least 2 GPUs to test multi-GPU device handling")
        print("   Testing with single GPU instead...")
        device_map = None
    else:
        print("✅ Multi-GPU environment detected, testing device_map='auto'")
        device_map = "auto"
    
    # Create minimal config for testing
    model_config = ModelConfig(
        model_name="microsoft/DialoGPT-small",  # Small model for testing
        use_flash_attention=False,  # Disable for compatibility
        torch_dtype="float32",
        device_map=device_map,
        trust_remote_code=False,
    )
    
    dataset_config = DatasetConfig(
        dataset_name="togethercomputer/RedPajama-Data-1T-Sample",
        dataset_split="train",
        text_column="text",
        max_seq_length=128,  # Small for testing
        chunk_size=128,
        streaming=False,
        num_proc=1,
        trust_remote_code=True,
    )
    
    training_config = TrainingConfig(
        total_training_tokens=1000,  # Very small for testing
        batch_size=2,
        learning_rate=3e-4,
        l1_coefficient=1e-3,
        use_wandb=False,
    )
    
    config = SAEConfig(
        model=model_config,
        dataset=dataset_config,
        training=training_config,
        architecture="standard",
        expansion_factor=4,
        hook_layer=1,
        hook_name="transformer.h.{layer}.mlp",
        activation_fn="relu",
        normalize_decoder=True,
        device="cuda:0" if torch.cuda.is_available() else "cpu",
        dtype="float32",
        seed=42,
    )
    
    try:
        # Load model
        print("📥 Loading model...")
        model_loader = UniversalModelLoader(config.model)
        model, tokenizer = model_loader.load_model_and_tokenizer()
        
        # Print model device information
        print(f"🔍 Model device information:")
        first_param_device = next(model.parameters()).device
        print(f"   First parameter device: {first_param_device}")
        
        # Check if model has distributed layers
        if hasattr(model, 'hf_device_map'):
            print(f"   HF device map: {model.hf_device_map}")
        
        # Setup dataset manager
        print("📊 Setting up dataset...")
        dataset_manager = DatasetManager(config.dataset, tokenizer)
        dataset_manager.load_dataset()
        dataset_manager.preprocess_dataset()
        
        # Test activations store device detection
        print("🎯 Testing ActivationsStore device detection...")
        activations_store = ActivationsStore(
            model, tokenizer, config, dataset_manager
        )
        
        print(f"   Detected input device: {activations_store._input_device}")
        
        # Test a small batch
        print("🧪 Testing small batch processing...")
        try:
            # Get a small sample
            dataloader = dataset_manager.get_dataloader(batch_size=1, shuffle=False)
            batch = next(iter(dataloader))
            
            # Test device placement
            input_ids = batch["input_ids"].to(activations_store._input_device)
            print(f"   Input tensor device: {input_ids.device}")
            
            # Test forward pass
            with torch.no_grad():
                model.eval()
                outputs = model(input_ids)
                print(f"   Forward pass successful!")
                print(f"   Output device: {outputs.logits.device}")
            
        except Exception as e:
            print(f"❌ Batch processing failed: {e}")
            return False
            
        print("✅ Device detection test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_device_detection()
    if success:
        print("\n🎉 All tests passed! The device mismatch fix should work.")
    else:
        print("\n💥 Tests failed. Please check the error messages above.")
