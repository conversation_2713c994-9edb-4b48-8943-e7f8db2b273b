"""
Improved Sparse Auto-Encoder (SAE) implementation.

This module provides a unified SAE implementation that works with any
transformer model architecture and supports multiple SAE variants.
"""

import logging
from typing import Optional, Dict, Any, Tuple, Union, Literal
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import Tensor
from dataclasses import dataclass
import math

logger = logging.getLogger(__name__)


@dataclass
class SAEOutput:
    """Output from SAE forward pass."""

    # Reconstructed input
    sae_out: Tensor
    """Reconstructed input from SAE"""

    # Feature activations
    feature_acts: Tensor
    """SAE feature activations"""

    # Loss components
    mse_loss: Tensor
    """Mean squared error reconstruction loss"""

    l1_loss: Tensor
    """L1 sparsity loss on feature activations"""

    # Additional metrics
    fvu: Tensor
    """Fraction of variance unexplained"""

    sparsity: Tensor
    """Fraction of active features"""

    # Optional auxiliary outputs
    aux_loss: Optional[Tensor] = None
    """Auxiliary loss (e.g., for gated architectures)"""

    hidden_pre: Optional[Tensor] = None
    """Pre-activation hidden states"""

    def __iter__(self):
        """Make SAEOutput iterable for DataParallel compatibility."""
        return iter(
            [
                self.sae_out,
                self.feature_acts,
                self.mse_loss,
                self.l1_loss,
                self.fvu,
                self.sparsity,
                self.aux_loss,
                self.hidden_pre,
            ]
        )

    @classmethod
    def from_tuple(cls, values):
        """Reconstruct SAEOutput from tuple (for DataParallel)."""
        return cls(
            sae_out=values[0],
            feature_acts=values[1],
            mse_loss=values[2],
            l1_loss=values[3],
            fvu=values[4],
            sparsity=values[5],
            aux_loss=values[6],
            hidden_pre=values[7],
        )


class SAE(nn.Module):
    """
    Sparse Auto-Encoder with support for multiple architectures.

    Supports standard, gated, and JumpReLU architectures with
    configurable activation functions and normalization.
    """

    def __init__(
        self,
        d_in: int,
        d_sae: int,
        architecture: Literal["standard", "gated", "jumprelu"] = "standard",
        activation_fn: str = "relu",
        normalize_decoder: bool = True,
        bias_decoder: bool = True,
        device: Union[str, torch.device] = "cuda",
        dtype: torch.dtype = torch.float32,
    ):
        """
        Initialize SAE.

        Args:
            d_in: Input dimension
            d_sae: SAE hidden dimension
            architecture: SAE architecture type
            activation_fn: Activation function name
            normalize_decoder: Whether to normalize decoder weights
            bias_decoder: Whether to use bias in decoder
            device: Device for computation
            dtype: Data type for parameters
        """
        super().__init__()

        self.d_in = d_in
        self.d_sae = d_sae
        self.architecture = architecture
        self.activation_fn_name = activation_fn
        self._normalize_decoder = normalize_decoder
        self.bias_decoder = bias_decoder
        self.device = device
        self.dtype = dtype

        # Initialize activation function
        self.activation_fn = self._get_activation_fn(activation_fn)

        # Initialize parameters based on architecture
        self._init_parameters()

        # Move to device and set dtype
        self.to(device=device, dtype=dtype)

        # Initialize weights
        self._init_weights()

    def load(self, path: str):
        """Load SAE from either a .pt file or huggingface hub."""
        if path.endswith(".pt"):
            self.load_state_dict(torch.load(path, map_location=self.device))
        else:
            self = SAE.load_from_hub(path, device=self.device)

    def _get_activation_fn(self, activation_fn: str) -> nn.Module:
        """Get activation function by name."""
        activation_map = {
            "relu": nn.ReLU(),
            "gelu": nn.GELU(),
            "swish": nn.SiLU(),
            "tanh": nn.Tanh(),
            "sigmoid": nn.Sigmoid(),
        }

        if activation_fn not in activation_map:
            raise ValueError(f"Unsupported activation function: {activation_fn}")

        return activation_map[activation_fn]

    def _init_parameters(self):
        """Initialize SAE parameters based on architecture."""
        if self.architecture == "standard":
            self._init_standard_parameters()
        elif self.architecture == "gated":
            self._init_gated_parameters()
        elif self.architecture == "jumprelu":
            self._init_jumprelu_parameters()
        else:
            raise ValueError(f"Unsupported architecture: {self.architecture}")

    def _init_standard_parameters(self):
        """Initialize parameters for standard SAE."""
        # Encoder: input -> hidden
        self.W_enc = nn.Parameter(torch.empty(self.d_in, self.d_sae))
        self.b_enc = nn.Parameter(torch.zeros(self.d_sae))

        # Decoder: hidden -> input
        self.W_dec = nn.Parameter(torch.empty(self.d_sae, self.d_in))
        if self.bias_decoder:
            self.b_dec = nn.Parameter(torch.zeros(self.d_in))
        else:
            self.register_parameter("b_dec", None)

    def _init_gated_parameters(self):
        """Initialize parameters for gated SAE."""
        # Gated architecture has separate gating and magnitude paths
        self.W_enc = nn.Parameter(torch.empty(self.d_in, self.d_sae))
        self.W_gate = nn.Parameter(torch.empty(self.d_in, self.d_sae))
        self.b_enc = nn.Parameter(torch.zeros(self.d_sae))
        self.b_gate = nn.Parameter(torch.zeros(self.d_sae))

        # Shared decoder
        self.W_dec = nn.Parameter(torch.empty(self.d_sae, self.d_in))
        if self.bias_decoder:
            self.b_dec = nn.Parameter(torch.zeros(self.d_in))
        else:
            self.register_parameter("b_dec", None)

    def _init_jumprelu_parameters(self):
        """Initialize parameters for JumpReLU SAE."""
        # JumpReLU has threshold parameters
        self.W_enc = nn.Parameter(torch.empty(self.d_in, self.d_sae))
        self.b_enc = nn.Parameter(torch.zeros(self.d_sae))
        self.threshold = nn.Parameter(torch.zeros(self.d_sae))

        # Decoder
        self.W_dec = nn.Parameter(torch.empty(self.d_sae, self.d_in))
        if self.bias_decoder:
            self.b_dec = nn.Parameter(torch.zeros(self.d_in))
        else:
            self.register_parameter("b_dec", None)

    def _init_weights(self):
        """Initialize weights using appropriate schemes."""
        # Xavier/Glorot initialization for encoder
        if hasattr(self, "W_enc"):
            nn.init.xavier_uniform_(self.W_enc)

        if hasattr(self, "W_gate"):
            nn.init.xavier_uniform_(self.W_gate)

        # Initialize decoder as transpose of encoder (tied weights concept)
        if hasattr(self, "W_dec"):
            with torch.no_grad():
                self.W_dec.data = self.W_enc.data.T.clone()

        # Normalize decoder weights if requested
        if self._normalize_decoder:
            self._normalize_decoder_weights()

    def _normalize_decoder_weights(self):
        """Normalize decoder weights to unit norm."""
        with torch.no_grad():
            self.W_dec.data = F.normalize(self.W_dec.data, dim=1)

    def encode(self, x: Tensor) -> Tensor:
        """
        Encode input to feature activations.

        Args:
            x: Input tensor of shape (..., d_in)

        Returns:
            Feature activations of shape (..., d_sae)
        """
        if self.architecture == "standard":
            return self._encode_standard(x)
        elif self.architecture == "gated":
            return self._encode_gated(x)
        elif self.architecture == "jumprelu":
            return self._encode_jumprelu(x)
        else:
            raise ValueError(f"Unsupported architecture: {self.architecture}")

    def _encode_standard(self, x: Tensor) -> Tensor:
        """Standard SAE encoding."""
        # Remove decoder bias if present
        if self.b_dec is not None:
            x_centered = x - self.b_dec
        else:
            x_centered = x

        # Linear transformation + activation
        hidden_pre = x_centered @ self.W_enc + self.b_enc
        return self.activation_fn(hidden_pre)

    def _encode_gated(self, x: Tensor) -> Tensor:
        """Gated SAE encoding."""
        # Remove decoder bias if present
        if self.b_dec is not None:
            x_centered = x - self.b_dec
        else:
            x_centered = x

        # Compute gate and magnitude
        gate_pre = x_centered @ self.W_gate + self.b_gate
        mag_pre = x_centered @ self.W_enc + self.b_enc

        # Apply gating
        gate = torch.sigmoid(gate_pre)
        magnitude = self.activation_fn(mag_pre)

        return gate * magnitude

    def _encode_jumprelu(self, x: Tensor) -> Tensor:
        """JumpReLU SAE encoding."""
        # Remove decoder bias if present
        if self.b_dec is not None:
            x_centered = x - self.b_dec
        else:
            x_centered = x

        # Linear transformation
        hidden_pre = x_centered @ self.W_enc + self.b_enc

        # JumpReLU: ReLU with learnable threshold
        return F.relu(hidden_pre - self.threshold)

    def decode(self, feature_acts: Tensor) -> Tensor:
        """
        Decode feature activations to reconstruction.

        Args:
            feature_acts: Feature activations of shape (..., d_sae)

        Returns:
            Reconstructed input of shape (..., d_in)
        """
        reconstruction = feature_acts @ self.W_dec

        if self.b_dec is not None:
            reconstruction = reconstruction + self.b_dec

        return reconstruction

    def forward(self, x: Tensor, return_aux_losses: bool = True) -> SAEOutput:
        """
        Forward pass through SAE.

        Args:
            x: Input tensor of shape (..., d_in)
            return_aux_losses: Whether to compute auxiliary losses

        Returns:
            SAEOutput containing reconstruction and losses
        """
        # Encode
        feature_acts = self.encode(x)

        # Decode
        sae_out = self.decode(feature_acts)

        # Compute losses
        mse_loss = F.mse_loss(sae_out, x, reduction="mean")
        l1_loss = feature_acts.abs().mean()

        # Compute metrics
        residual = sae_out - x
        total_variance = (x - x.mean(dim=-1, keepdim=True)).pow(2).sum(dim=-1)
        residual_variance = residual.pow(2).sum(dim=-1)
        fvu = (residual_variance / (total_variance + 1e-8)).mean()

        # Compute sparsity (fraction of active features)
        sparsity = (feature_acts > 0).float().mean()

        return SAEOutput(
            sae_out=sae_out,
            feature_acts=feature_acts,
            mse_loss=mse_loss,
            l1_loss=l1_loss,
            fvu=fvu,
            sparsity=sparsity,
        )

    def get_feature_density(self, x: Tensor, threshold: float = 1e-6) -> Tensor:
        """
        Compute feature activation density.

        Args:
            x: Input tensor
            threshold: Threshold for considering a feature active

        Returns:
            Feature density for each feature
        """
        with torch.no_grad():
            feature_acts = self.encode(x)
            return (feature_acts > threshold).float().mean(dim=0)

    def normalize_decoder(self):
        """Normalize decoder weights (can be called during training)."""
        if self._normalize_decoder:
            self._normalize_decoder_weights()


class TrainingSAE(SAE):
    """
    SAE variant optimized for training with additional features.

    Includes dead neuron handling, feature resampling, and training utilities.
    """

    def __init__(self, *args, **kwargs):
        """Initialize training SAE."""
        super().__init__(*args, **kwargs)

        # Training state
        self.training_step = 0
        self.feature_activation_counts = torch.zeros(self.d_sae, device=self.device)
        self.dead_feature_threshold = 1e-8

    def training_forward(
        self,
        x: Tensor,
        l1_coefficient: float = 1e-3,
        dead_feature_mask: Optional[Tensor] = None,
    ) -> SAEOutput:
        """
        Training-specific forward pass with additional losses.

        Args:
            x: Input tensor
            l1_coefficient: L1 regularization coefficient
            dead_feature_mask: Mask for dead features

        Returns:
            SAEOutput with training losses
        """
        # Standard forward pass
        output = self.forward(x, return_aux_losses=True)

        # Scale L1 loss by coefficient
        output.l1_loss = output.l1_loss * l1_coefficient

        # Update feature activation counts
        with torch.no_grad():
            active_features = (output.feature_acts > 0).float().sum(dim=0)
            self.feature_activation_counts += active_features

        # Handle dead features if mask provided
        if dead_feature_mask is not None:
            # Apply penalty to dead features
            dead_penalty = (output.feature_acts * dead_feature_mask).abs().mean()
            output.aux_loss = dead_penalty

        self.training_step += 1
        return output

    def get_dead_features(self, min_activations: int = 10) -> Tensor:
        """
        Identify dead features based on activation counts.

        Args:
            min_activations: Minimum activations to not be considered dead

        Returns:
            Boolean mask of dead features
        """
        return self.feature_activation_counts < min_activations

    def reset_dead_features(self, dead_mask: Tensor, input_sample: Tensor):
        """
        Reset dead features using input statistics.

        Args:
            dead_mask: Boolean mask of dead features
            input_sample: Sample of input data for reinitialization
        """
        if not dead_mask.any():
            return

        with torch.no_grad():
            # Reinitialize encoder weights for dead features
            n_dead = dead_mask.sum().item()

            # Sample random directions from input
            random_indices = torch.randint(0, input_sample.shape[0], (n_dead,))
            new_directions = input_sample[random_indices]

            # Normalize and assign to dead features
            new_directions = F.normalize(new_directions, dim=1)
            self.W_enc.data[:, dead_mask] = new_directions.T

            # Reset decoder weights (transpose of encoder)
            self.W_dec.data[dead_mask, :] = new_directions

            # Reset biases
            self.b_enc.data[dead_mask] = 0

            # Reset activation counts
            self.feature_activation_counts[dead_mask] = 0

        logger.info(f"Reset {n_dead} dead features")
